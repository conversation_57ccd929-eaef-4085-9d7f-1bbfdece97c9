import { useCallback, useEffect, useState } from "react";
import utils from "../utils";


export default () => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const fetchData = useCallback(async () => {
        setLoading(true);
        const resp = await utils.request({
            api: '/api/appConfig/data'
        })
        setLoading(false);
        if (resp.status !== 0) return;
        setData(resp.model)
    }, [])
    useEffect(() => {
        if (loading) return;
        fetchData();
    }, [fetchData])
    return { data, fetchData, loading };
}   