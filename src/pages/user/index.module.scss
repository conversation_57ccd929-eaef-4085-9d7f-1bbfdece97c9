.root {
    //单元格
    --nutui-cell-group-background-color: var(--app-card-background);
    --nutui-cell-background-color: var(--app-card-background);
    --nutui-cell-padding: 26rpx 32rpx;

    .dialogContent {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20rpx;

        .avatar {
            display: flex;
            padding: 0;
            border-radius: 200rpx;
            border: 1px solid var(--app-primary-color);
            align-items: center;
            justify-content: center;
        }

        .nickname {
            border: 1px solid var(--app-primary-color);
            border-radius: 200rpx;
            padding: 10rpx 20rpx;
        }
    }

    .userInfo {
        width: 100%;
        padding: 40rpx;
        display: flex;
        align-items: center;
        gap: 20rpx;

        .name {
            color: var(--app-text-color-primary);
            font-size: var(--nutui-font-text);
            font-weight: bolder;
            display: flex;
            align-items: center;
            gap: 10rpx;
        }
    }

    .items {
        margin: 20rpx;
        display: flex;
        flex-direction: column;
        gap: 20rpx;
        background: var(--app-card-background);
        border-radius: 20rpx;
        overflow: hidden;

        :global {
            .nut-cell-group-title {
                margin: 20rpx 0 0 0;
                color: var(--app-primary-color);
                font-size: var(--nutui-font-text);
                line-height: var(--nutui-font-text);
                font-weight: bolder;
            }

            .nut-cell-group-wrap {
                margin: unset;
            }
        }

        .title,
        .extra {
            display: flex;
            align-items: center;
            gap: 10rpx;
            font-size: var(--nutui-font-text-small);
            line-height: var(--nutui-font-text);
        }
    }
}