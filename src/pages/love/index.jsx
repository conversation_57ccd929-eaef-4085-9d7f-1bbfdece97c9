import GbTabbar from "../../components/GbTabbar";
import GbNavbar from "../../components/GbNavbar";
import GbBody from "../../components/GbBody";
import GbPage from "../../components/GbPage";
import styles from "./index.module.scss";
import BbLoveCardList from "../../components/Bb/BbLoveCardList";

const Love = () => {
	return (
		<GbPage className={styles.root}>
			<GbBody hasTabbar inNavbar full>
				<GbNavbar
					title="情侣动态"
					background="transparent"
					color="var(--app-primary-color)"
				/>
				<BbLoveCardList />
			</GbBody>
			<GbTabbar value={1} />
		</GbPage>
	);
};

export default Love;
