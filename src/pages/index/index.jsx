import GbTabbar from "../../components/GbTabbar";
import GbNavbar from "../../components/GbNavbar";
import GbBody from "../../components/GbBody";
import GbPage from "../../components/GbPage";
import styles from "./index.module.scss";
import { GbAvatarGroup } from "../../components/GbAvatar";
import { Add } from "@nutui/icons-react-taro";
import GbButton from "../../components/GbButton";
import BbFunctionCards from "../../components/Bb/BbFunctionCards";
import utils from "../../common/utils";
import GbImage from "../../components/GbImage";
import { Animate } from "@nutui/nutui-react-taro";
import { Text, View } from "@tarojs/components";
import useAppConfig from "../../common/hooks/useAppConfig";

const Index = () => {
	const { data } = useAppConfig();
	const { thinkTaIcon, homeBgImage } = data || {};
	return (
		<GbPage className={styles.root}>
			<GbBody
				hasTabbar
				inNavbar
				full
				background={"url(" + utils.getImageUrl(homeBgImage) + ")"}
				style={{
					backgroundSize: "cover",
					backgroundPosition: "center",
					backgroundRepeat: "no-repeat",
				}}
			>
				<GbNavbar title="情侣空间" background="transparent" />
				<GbAvatarGroup
					style={{ margin: "80rpx auto" }}
					max={2}
					list={[
						{
							src: utils.getImageUrl(thinkTaIcon),
						},
						{
							icon: <Add />,
						},
					]}
				/>
				<GbButton
					style={{
						margin: "0 auto",
						borderRadius: "999rpx !important",
						paddingLeft: "80rpx !important",
						paddingRight: "80rpx !important",
						background: "var(--app-primary-color)",
					}}
				>
					邀请另一半开通情侣空间
				</GbButton>
				<View
					style={{
						display: "flex",
						flexDirection: "column",
						justifyContent: "center",
						alignItems: "center",
						margin: "0 auto",
						width: "200rpx",
						height: "200rpx",
						background: "radial-gradient(circle, #fa6b84 20%, #fa6b8480 100%)",
						borderRadius: "50%",
						boxShadow: "0 0 20rpx #fa6b84",
					}}
				>
					<Animate type="ripple" loop>
						<GbImage
							src={utils.getImageUrl(thinkTaIcon)}
							width="100rpx"
							height="100rpx"
						/>
					</Animate>
					<Text style={{ color: "#fff", fontWeight: "bold" }}>想Ta</Text>
				</View>
				<BbFunctionCards />
			</GbBody>
			<GbTabbar value={0} />
		</GbPage>
	);
};

export default Index;
