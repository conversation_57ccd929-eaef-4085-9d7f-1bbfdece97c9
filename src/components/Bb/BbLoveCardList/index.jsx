import { Cell, Pagination } from "@nutui/nutui-react-taro";
import styles from "./index.module.scss";
import { ArrowDown, ArrowLeft, ArrowRight } from "@nutui/icons-react-taro";
import { Text, View } from "@tarojs/components";
import { useCallback, useEffect, useState } from "react";
import utils from "../../../common/utils";
import GbImagePreview from "../../GbImagePreview";
import GbInput from "../../GbInput";
import GbButton from "../../GbButton";

const BbLoveCardList = () => {
	const [data, setData] = useState(null);
	const [loading, setLoading] = useState(false);
	const [pageNo, setPageNo] = useState(1);
	const [totalPages, setTotalPages] = useState(0);
	const pageSize = 20;
	const fetchData = useCallback(async () => {
		setLoading(true);
		const resp = await utils.request({
			api: "/api/love/page",
			data: {
				query: {
					pageNo: pageNo,
					pageSize: pageSize,
					status: "FINISHED",
					sortOrder: -1,
					sortField: "finishTime",
				},
			},
		});
		setLoading(false);
		if (resp.status !== 0) return;
		setData(resp.page.content);
		setTotalPages(resp.page.totalPages);
	}, [pageNo]);

	useEffect(() => {
		if (loading) return;
		fetchData();
	}, [fetchData]);
	return (
		<View className={styles.root}>
			{data?.map((item, index) => (
				<CardItem key={index} item={item} />
			))}
			<View className={styles.pagination}>
				<Pagination
					value={pageNo}
					total={totalPages}
					pageSize={pageSize}
					onChange={setPageNo}
					prev={<ArrowLeft />}
					next={<ArrowRight />}
				/>
			</View>
		</View>
	);
};

export default BbLoveCardList;

const CardItem = ({ item }) => {
	const { title, finishTime, photos, commentCount, comments } = item || {};
	const [showComments, setShowComments] = useState(false);
	return (
		<Cell.Group>
			<Cell
				title={
					<View>
						<Text
							style={{ fontWeight: "bold", color: "var(--app-primary-color)" }}
						>
							{title}
						</Text>
						<View
							style={{
								color: "#8C8C8C",
								fontSize: "20rpx",
								lineHeight: 1.5,
							}}
						>
							{finishTime && utils.formatTime(finishTime)}
						</View>
					</View>
				}
			/>
			{photos && photos.length && (
				<Cell>
					<GbImagePreview imageList={photos} />
				</Cell>
			)}
			<Cell
				style={{ paddingTop: "14rpx", paddingBottom: "14rpx" }}
				title={
					<Text
						style={{ color: "var(--app-primary-color)", fontSize: "20rpx" }}
					>
						❤️ 日常碎碎念 ({commentCount})
					</Text>
				}
				extra={
					<ArrowDown
						width="14"
						style={{
							transform: `rotate(${showComments ? 180 : 0}deg)`,
							transition: "all 0.3s ease-in-out",
						}}
						onClick={() => setShowComments(!showComments)}
					/>
				}
			/>
			{showComments && (
				<Cell
					style={{
						paddingTop: "10rpx",
					}}
				>
					<View
						style={{
							display: "flex",
							flexDirection: "column",
							gap: "8rpx",
							width: "100%",
						}}
					>
						{comments?.map((data, index) => (
							<Text
								key={index}
								style={{
									color: "#8C8C8C",
									fontSize: "20rpx",
									lineHeight: "20rpx",
									wordBreak: "break-all",
								}}
							>
								{data.name}: {data.content}
							</Text>
						))}
						<View
							style={{
								marginTop: "10rpx",
								display: "flex",
								gap: "20rpx",
								width: "100%",
								alignItems: "center",
							}}
						>
							<GbInput style={{ flex: 1 }} />
							<GbButton size="small" style={{ fontSize: "28rpx !important" }}>
								发送
							</GbButton>
						</View>
					</View>
				</Cell>
			)}
		</Cell.Group>
	);
};
