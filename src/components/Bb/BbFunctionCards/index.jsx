import { View } from "@tarojs/components";
import styles from "./index.module.scss";
import { Calendar, Gift, Check } from "@nutui/icons-react-taro";

const BbFunctionCards = () => {
	const handleCardClick = (cardType) => {
		// 在这里处理卡片点击事件
		console.log(`点击了${cardType}卡片`);
	};

	return (
		<View className={styles.root}>
			{/* 每日打卡卡片 */}
			<Card
				title="每日打卡"
				subtitle="今日已打卡"
				icon={<Calendar color="#fff" />}
				bgColor="#FF6B9D"
				onClick={() => handleCardClick("每日打卡")}
			/>

			{/* 我们的纪念日卡片 */}
			<Card
				title="纪念日"
				subtitle="3个纪念日"
				icon={<Gift color="#fff" />}
				bgColor="#9C88FF"
				onClick={() => handleCardClick("纪念日")}
			/>

			{/* 情侣任务卡片 */}
			<Card
				title="情侣任务"
				subtitle="2个待完成"
				icon={<Check color="#fff" />}
				bgColor="#7DD3FC"
				onClick={() => handleCardClick("情侣任务")}
			/>
		</View>
	);
};

export default BbFunctionCards;

const Card = ({ title, subtitle, icon, bgColor, onClick }) => {
	return (
		<View
			className={styles.card}
			style={{ backgroundColor: bgColor }}
			onClick={onClick}
		>
			<View className={styles.iconContainer}>{icon}</View>
			<View className={styles.content}>
				<View className={styles.title}>{title}</View>
				<View className={styles.subtitle}>{subtitle}</View>
			</View>
		</View>
	);
};
