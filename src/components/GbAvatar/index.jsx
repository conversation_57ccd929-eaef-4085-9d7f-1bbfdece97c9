import { Avatar } from "@nutui/nutui-react-taro";
import styles from "./index.module.scss";

const GbAvatar = ({ ...restProps }) => {
	return (
		<Avatar
			size="large"
			background="var(--app-background)"
			color="var(--app-primary-color)"
			style={{ lineHeight: "unset" }}
			{...restProps}
		/>
	);
};

const GbAvatarGroup = ({ list, ...restProps }) => {
	return (
		<Avatar.Group
			className={styles.GbAvatarGroup}
			maxBackground="var(--app-background)"
			maxColor="var(--app-primary-color)"
			{...restProps}
		>
			{list.map((item, index) => (
				<GbAvatar key={index} {...item} />
			))}
		</Avatar.Group>
	);
};

export default GbAvatar;
export { GbAvatarGroup };
