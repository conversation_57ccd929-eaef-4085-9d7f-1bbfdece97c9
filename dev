#!/bin/bash

echo "           _____            _____                    _____                    _____                _____ ";
echo "          /\    \          /\    \                  /\    \                  /\    \              |\    \ ";
echo "         /::\____\        /::\____\                /::\    \                /::\____\             |:\____\ ";
echo "        /:::/    /       /:::/    /               /::::\    \              /:::/    /             |::|   | ";
echo "       /:::/    /       /:::/    /               /::::::\    \            /:::/    /              |::|   | ";
echo "      /:::/    /       /:::/    /               /:::/\:::\    \          /:::/    /               |::|   | ";
echo "     /:::/    /       /:::/    /               /:::/  \:::\    \        /:::/____/                |::|   | ";
echo "    /:::/    /       /:::/    /               /:::/    \:::\    \      /::::\    \                |::|   | ";
echo "   /:::/    /       /:::/    /      _____    /:::/    / \:::\    \    /::::::\____\________       |::|___|______ ";
echo "  /:::/    /       /:::/____/      /\    \  /:::/    /   \:::\    \  /:::/\:::::::::::\    \      /::::::::\    \ ";
echo " /:::/____/       |:::|    /      /::\____\/:::/____/     \:::\____\/:::/  |:::::::::::\____\    /::::::::::\____\ ";
echo " \:::\    \       |:::|____\     /:::/    /\:::\    \      \::/    /\::/   |::|~~~|~~~~~        /:::/~~~~/~~ ";
echo "  \:::\    \       \:::\    \   /:::/    /  \:::\    \      \/____/  \/____|::|   |            /:::/    / ";
echo "   \:::\    \       \:::\    \ /:::/    /    \:::\    \                    |::|   |           /:::/    / ";
echo "    \:::\    \       \:::\    /:::/    /      \:::\    \                   |::|   |          /:::/    / ";
echo "     \:::\    \       \:::\__/:::/    /        \:::\    \                  |::|   |          \::/    / ";
echo "      \:::\    \       \::::::::/    /          \:::\    \                 |::|   |           \/____/ ";
echo "       \:::\    \       \::::::/    /            \:::\    \                |::|   | ";
echo "        \:::\____\       \::::/    /              \:::\____\               \::|   | ";
echo "         \::/    /        \::/____/                \::/    /                \:|   | ";
echo "          \/____/          ~~                       \/____/                  \|___| ";

echo " "

echo "编程如禅,调试如炼";
echo "Bug生生不息,我心岿然不动"
echo "需求千变,唯代码永恒"
echo "产品一句改动,胜过千行重构"
echo "提交需谨慎,回滚需果断"
echo "删库跑路不可取,备份才是正道"
echo "白天写代码,晚上修Bug"
echo "上线如登仙,宕机似入地狱"
echo "愿此生不写Bug,不踩坑,不背锅"
echo "愿服务器长青,需求稳定如初"
echo " "
echo "            —— 愿程序员秃有所值,Bug退散! 🚀😆"

echo " "

# 删除/dist目录
# rm -rf dist
# if [ $? -eq 0 ]; then
#     echo "删除dist目录成功"
# else
#     echo "删除dist目录失败"
#     exit 1
# fi


echo "检查代码中..."
# ESLint检查src目录
npx eslint src --ext .js,.jsx,.ts,.tsx
if [ $? -eq 0 ]; then
    echo "代码通过ESLint检查"
else
    echo "代码未通过ESLint检查,请检查代码"
    exit 1
fi


# echo "编程如禅,调试如炼";
# echo "Bug生生不息,我心岿然不动"
# echo "需求千变,唯代码永恒"
# echo "产品一句改动,胜过千行重构"
# echo "提交需谨慎,回滚需果断"
# echo "删库跑路不可取,备份才是正道"
# echo "白天写代码,晚上修Bug"
# echo "上线如登仙,宕机似入地狱"
# echo "愿此生不写Bug,不踩坑,不背锅"
# echo "愿服务器长青,需求稳定如初"
# echo "            —— 愿程序员秃有所值,Bug退散! 🚀😆"


# echo "编程如禅,调试如炼";
# echo "Bug生生不息,我发际线渐远";
# echo "需求千变,头发难全";
# echo "一夜回滚三千行,只换产品一声“改改”";
# echo "提交需谨慎,回滚需果断";
# echo "删库跑路不可取,肝秃才是正道";
# echo "白天写代码,晚上修Bug";
# echo "脱发如秋叶,秃头胜菩萨";
# echo "愿此生不写Bug,不踩坑,不背锅";
# echo "若终须秃,愿换来代码无错,永不加班！"
# echo "            —— 愿程序员秃有所值，Bug 退散！ 🚀😆"
yarn dev:weapp
# echo " "
# # 打包本地微信小程序调试
# echo "谁最帅:"
# while true; do
#     read name

#     if [ "$name" == "徐育娟" ]; then
#         echo "真帅"
#         yarn dev:weapp
#         break
#     else 
#         echo "不对不对,再想想"
#     fi
# done