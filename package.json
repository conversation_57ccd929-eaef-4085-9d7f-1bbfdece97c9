{"name": "cloudCard", "version": "1.0.0", "private": true, "description": "游乐玩指是一款现代化的名片管理小程序，旨在帮助您在数字化时代轻松连接和管理人脉。通过云端技术，您可以随时随地创建、分享和保存名片，再也不用担心遗失或忘记携带名片。无论是商业会议、社交活动，还是日常交流，游乐玩指都能让您轻松交换信息，并高效管理您的联系人网络。", "templateInfo": {"name": "react-NutUI", "typescript": false, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@nutui/nutui-react-taro": "^2.3.0", "@tarojs/components": "4.0.9", "@tarojs/helper": "4.0.9", "@tarojs/plugin-framework-react": "4.0.9", "@tarojs/plugin-html": "4.0.9", "@tarojs/plugin-platform-alipay": "4.0.9", "@tarojs/plugin-platform-h5": "4.0.9", "@tarojs/plugin-platform-jd": "4.0.9", "@tarojs/plugin-platform-qq": "4.0.9", "@tarojs/plugin-platform-swan": "4.0.9", "@tarojs/plugin-platform-tt": "4.0.9", "@tarojs/plugin-platform-weapp": "4.0.9", "@tarojs/react": "4.0.9", "@tarojs/runtime": "4.0.9", "@tarojs/shared": "4.0.9", "@tarojs/taro": "4.0.9", "classnames": "^2.5.1", "dayjs": "^1.11.13", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-react": "^7.26.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.0.9", "@tarojs/taro-loader": "4.0.9", "@tarojs/webpack5-runner": "4.0.9", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "4.0.9", "eslint": "^8.12.0", "eslint-config-taro": "4.0.9", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "eslint-webpack-plugin": "^3.2.0", "postcss": "^8.4.18", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "ts-node": "^10.9.1", "webpack": "5.78.0"}, "prettier": {"useTabs": true, "tabWidth": 2}}